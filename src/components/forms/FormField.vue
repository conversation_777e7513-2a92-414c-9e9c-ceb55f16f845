<template>
  <div class="space-y-1 md:space-y-2">
    <!-- Label -->
    <label
      v-if="label"
      :for="id"
      class="block text-sm md:text-base font-medium text-gray-700"
    >
      {{ label }}
      <span v-if="required" class="text-red-500 ml-1">*</span>
    </label>

    <!-- Input Field -->
    <div class="relative">
      <!-- Text Input -->
      <input
        v-if="type === 'text' || type === 'email' || type === 'tel' || type === 'password' || type === 'number'"
        :id="id"
        :type="type"
        :value="modelValue"
        :placeholder="placeholder"
        :required="required"
        :disabled="disabled"
        :readonly="readonly"
        @input="$emit('update:modelValue', ($event.target as HTMLInputElement).value)"
        class="w-full px-3 md:px-4 py-2 md:py-3 text-sm md:text-base border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed transition-colors"
        :class="{ 'border-red-500': error }"
      />

      <!-- Textarea -->
      <textarea
        v-else-if="type === 'textarea'"
        :id="id"
        :value="modelValue"
        :placeholder="placeholder"
        :required="required"
        :disabled="disabled"
        :readonly="readonly"
        :rows="rows || 3"
        @input="$emit('update:modelValue', ($event.target as HTMLTextAreaElement).value)"
        class="w-full px-3 md:px-4 py-2 md:py-3 text-sm md:text-base border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed transition-colors resize-vertical"
        :class="{ 'border-red-500': error }"
      ></textarea>

      <!-- Select -->
      <select
        v-else-if="type === 'select'"
        :id="id"
        :value="modelValue"
        :required="required"
        :disabled="disabled"
        @change="$emit('update:modelValue', ($event.target as HTMLSelectElement).value)"
        class="w-full px-3 md:px-4 py-2 md:py-3 text-sm md:text-base border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed transition-colors"
        :class="{ 'border-red-500': error }"
      >
        <option v-if="placeholder" value="" disabled>{{ placeholder }}</option>
        <option v-for="option in options" :key="option.value" :value="option.value">
          {{ option.label }}
        </option>
      </select>

      <!-- Date Input -->
      <input
        v-else-if="type === 'date' || type === 'datetime-local'"
        :id="id"
        :type="type"
        :value="modelValue"
        :required="required"
        :disabled="disabled"
        :readonly="readonly"
        @input="$emit('update:modelValue', ($event.target as HTMLInputElement).value)"
        class="w-full px-3 md:px-4 py-2 md:py-3 text-sm md:text-base border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed transition-colors"
        :class="{ 'border-red-500': error }"
      />

      <!-- Checkbox -->
      <div v-else-if="type === 'checkbox'" class="flex items-center">
        <input
          :id="id"
          type="checkbox"
          :checked="modelValue"
          :required="required"
          :disabled="disabled"
          @change="$emit('update:modelValue', ($event.target as HTMLInputElement).checked)"
          class="h-4 w-4 md:h-5 md:w-5 text-blue-600 focus:ring-blue-500 border-gray-300 rounded disabled:cursor-not-allowed"
        />
        <label v-if="label" :for="id" class="ml-2 md:ml-3 text-sm md:text-base text-gray-700">
          {{ label }}
          <span v-if="required" class="text-red-500 ml-1">*</span>
        </label>
      </div>
    </div>

    <!-- Error Message -->
    <p v-if="error" class="text-sm md:text-base text-red-600">
      {{ error }}
    </p>

    <!-- Help Text -->
    <p v-if="help" class="text-xs md:text-sm text-gray-500">
      {{ help }}
    </p>
  </div>
</template>

<script setup lang="ts">
interface Option {
  value: string | number
  label: string
}

interface Props {
  id: string
  type: 'text' | 'email' | 'tel' | 'password' | 'number' | 'textarea' | 'select' | 'date' | 'datetime-local' | 'checkbox'
  modelValue: string | number | boolean
  label?: string
  placeholder?: string
  required?: boolean
  disabled?: boolean
  readonly?: boolean
  error?: string
  help?: string
  options?: Option[]
  rows?: number
}

defineProps<Props>()

defineEmits<{
  'update:modelValue': [value: string | number | boolean]
}>()
</script>
