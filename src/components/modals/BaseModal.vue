<template>
  <div class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen px-4 md:px-6 lg:px-8 pt-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div 
        class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" 
        @click="handleBackdropClick"
      ></div>

      <!-- Modal -->
      <div 
        class="inline-block w-full text-left align-middle transition-all transform bg-white shadow-xl rounded-lg"
        :class="modalSizeClasses"
      >
        <!-- Header -->
        <div class="flex items-center justify-between p-4 md:p-6 lg:p-8 border-b border-gray-200">
          <h3 class="text-base md:text-lg font-medium text-gray-900">
            {{ title }}
          </h3>
          <button 
            @click="$emit('close')" 
            class="text-gray-400 hover:text-gray-600 p-1 md:p-2 lg:p-3 rounded-md hover:bg-gray-100 transition-colors"
            :aria-label="'Close ' + title"
          >
            <svg class="w-5 h-5 md:w-6 md:h-6 lg:w-7 lg:h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <!-- Content -->
        <div class="p-4 md:p-6">
          <slot />
        </div>

        <!-- Footer (if provided) -->
        <div v-if="$slots.footer" class="flex items-center justify-end gap-3 md:gap-4 p-4 md:p-6 border-t border-gray-200 bg-gray-50">
          <slot name="footer" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  title: string
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  closeOnBackdrop?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  size: 'md',
  closeOnBackdrop: true
})

const emit = defineEmits<{
  close: []
}>()

const modalSizeClasses = computed(() => {
  const sizeMap = {
    sm: 'max-w-sm md:max-w-md',
    md: 'max-w-md md:max-w-lg',
    lg: 'max-w-lg md:max-w-xl',
    xl: 'max-w-xl md:max-w-2xl',
    full: 'max-w-full md:max-w-6xl mx-4 md:mx-8'
  }

  return `${sizeMap[props.size]} my-8 md:my-12 overflow-hidden`
})

const handleBackdropClick = () => {
  if (props.closeOnBackdrop) {
    emit('close')
  }
}
</script>
