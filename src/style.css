@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom responsive utilities */
@layer utilities {
  /* Responsive text utilities - Simplified without tablet/desktop variants */
  .text-responsive-xs {
    @apply text-xs;
  }

  .text-responsive-sm {
    @apply text-sm;
  }

  .text-responsive-base {
    @apply text-base;
  }

  .text-responsive-lg {
    @apply text-lg;
  }

  .text-responsive-xl {
    @apply text-lg;
  }

  .text-responsive-2xl {
    @apply text-xl;
  }

  /* Responsive spacing utilities - Simplified without tablet/desktop variants */
  .p-responsive {
    @apply p-4;
  }

  .px-responsive {
    @apply px-4;
  }

  .py-responsive {
    @apply py-4;
  }

  .m-responsive {
    @apply m-4;
  }

  .mx-responsive {
    @apply mx-4;
  }

  .my-responsive {
    @apply my-4;
  }

  .gap-responsive {
    @apply gap-4;
  }

  .space-x-responsive > * + * {
    @apply ml-4;
  }

  .space-y-responsive > * + * {
    @apply mt-4;
  }
}

/* Base responsive improvements */
@layer base {
  html {
    @apply scroll-smooth;
  }

  body {
    @apply antialiased;
  }

  /* Improve button touch targets on mobile */
  button, [role="button"] {
    @apply touch-manipulation;
  }

  /* Better focus styles */
  *:focus {
    @apply outline-none ring-2 ring-blue-500 ring-offset-2;
  }

  /* Responsive table improvements */
  .responsive-table {
    @apply min-w-full;
  }

  .responsive-table th,
  .responsive-table td {
    @apply px-4 py-3;
  }

  .responsive-table th {
    @apply text-xs;
  }

  .responsive-table td {
    @apply text-sm;
  }
}
